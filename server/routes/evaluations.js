const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const geminiService = require('../services/gemini');

const router = express.Router();
const EVALUATIONS_FILE = path.join(__dirname, '../data/evaluations.json');
const PROMPTS_FILE = path.join(__dirname, '../data/prompts.json');

// Helper function to read evaluations data
async function readEvaluationsData() {
  try {
    const data = await fs.readFile(EVALUATIONS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading evaluations data:', error);
    return { evaluations: [], nextId: 1 };
  }
}

// Helper function to write evaluations data
async function writeEvaluationsData(data) {
  try {
    await fs.writeFile(EVALUATIONS_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing evaluations data:', error);
    throw error;
  }
}

// Helper function to read prompts data
async function readPromptsData() {
  try {
    const data = await fs.readFile(PROMPTS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading prompts data:', error);
    return { prompts: [], nextId: 1 };
  }
}

// GET all evaluations
router.get('/', async (req, res) => {
  try {
    const data = await readEvaluationsData();
    res.json(data.evaluations);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch evaluations' });
  }
});

// POST run new evaluation
router.post('/run', async (req, res) => {
  try {
    const { input } = req.body;

    if (!input) {
      return res.status(400).json({ error: 'Input is required' });
    }

    // Validate the new input structure
    if (typeof input === 'object' && input.roleName && input.competencyGaps) {
      const { roleName, competencyGaps } = input;

      if (!roleName || !competencyGaps || competencyGaps.length === 0) {
        return res.status(400).json({
          error: 'Role name and competency gaps are required'
        });
      }
    } else {
      // For backward compatibility with old format
      if (typeof input === 'string' && !input.trim()) {
        return res.status(400).json({ error: 'Input text is required' });
      }
    }

    // Get current prompts
    const promptsData = await readPromptsData();
    const prompt1 = promptsData.prompts.find(p => p.id === 1);
    const prompt2 = promptsData.prompts.find(p => p.id === 2);

    if (!prompt1 || !prompt2) {
      return res.status(400).json({ error: 'Both prompts must be configured' });
    }

    // Run the prompt chain
    const result = await geminiService.runPromptChain(input, prompt1.content, prompt2.content);

    // Save evaluation result
    const evaluationsData = await readEvaluationsData();

    // Format input for display and extract objectives
    let formattedInput = input;
    let objectives = [];

    if (typeof input === 'object' && input.roleName && input.competencyGaps) {
      // Format competency gaps for clean display
      const competencyGapsList = input.competencyGaps.map(gap =>
        `${gap.competency_name}: ${gap.gap_percentage}%`
      ).join(', ');
      formattedInput = `Role: ${input.roleName}\nCompetency Gaps: ${competencyGapsList}`;

      // Extract objectives from result if available
      if (result.objectives) {
        objectives = result.objectives;
      } else if (result.step1 && result.step1.output) {
        try {
          // Fallback: Parse objectives from step1 output
          const objectivesMatch = result.step1.output.match(/Generated objectives: (\[.*\])/);
          if (objectivesMatch) {
            objectives = JSON.parse(objectivesMatch[1]);
          }
        } catch (error) {
          console.error('Error parsing objectives:', error);
          objectives = [];
        }
      }
    }

    const evaluation = {
      id: evaluationsData.nextId,
      input,
      formattedInput,
      objectives,
      output: result.finalOutput,
      prompt1Version: prompt1.version,
      prompt2Version: prompt2.version,
      prompt1Content: prompt1.content,
      prompt2Content: prompt2.content,
      timestamp: new Date().toISOString(),
      details: result
    };

    evaluationsData.evaluations.push(evaluation);
    evaluationsData.nextId++;

    await writeEvaluationsData(evaluationsData);

    res.json(evaluation);
  } catch (error) {
    console.error('Error running evaluation:', error);
    res.status(500).json({ error: 'Failed to run evaluation' });
  }
});

module.exports = router;

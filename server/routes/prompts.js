const express = require('express');
const fs = require('fs').promises;
const path = require('path');

const router = express.Router();
const PROMPTS_FILE = path.join(__dirname, '../data/prompts.json');

// Helper function to read prompts data
async function readPromptsData() {
  try {
    const data = await fs.readFile(PROMPTS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading prompts data:', error);
    return { prompts: [], nextId: 1 };
  }
}

// Helper function to write prompts data
async function writePromptsData(data) {
  try {
    await fs.writeFile(PROMPTS_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error writing prompts data:', error);
    throw error;
  }
}

// GET all prompts
router.get('/', async (req, res) => {
  try {
    const data = await readPromptsData();
    res.json(data.prompts);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch prompts' });
  }
});

// GET specific prompt by ID
router.get('/:id', async (req, res) => {
  try {
    const data = await readPromptsData();
    const prompt = data.prompts.find(p => p.id === parseInt(req.params.id));
    if (!prompt) {
      return res.status(404).json({ error: 'Prompt not found' });
    }
    res.json(prompt);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch prompt' });
  }
});

// PUT update prompt (creates new version)
router.put('/:id', async (req, res) => {
  try {
    const { content } = req.body;
    const data = await readPromptsData();
    const promptIndex = data.prompts.findIndex(p => p.id === parseInt(req.params.id));

    if (promptIndex === -1) {
      return res.status(404).json({ error: 'Prompt not found' });
    }

    const currentPrompt = data.prompts[promptIndex];

    // Initialize history array if it doesn't exist
    if (!currentPrompt.history) {
      currentPrompt.history = [{
        version: currentPrompt.version,
        content: currentPrompt.content,
        updatedAt: currentPrompt.updatedAt
      }];
    }

    // Add current version to history before updating
    currentPrompt.history.push({
      version: currentPrompt.version,
      content: currentPrompt.content,
      updatedAt: currentPrompt.updatedAt
    });

    // Update the prompt with new version
    data.prompts[promptIndex] = {
      ...currentPrompt,
      content,
      version: currentPrompt.version + 1,
      updatedAt: new Date().toISOString()
    };

    await writePromptsData(data);
    res.json(data.prompts[promptIndex]);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update prompt' });
  }
});

// GET specific prompt version
router.get('/:id/versions/:version', async (req, res) => {
  try {
    const data = await readPromptsData();
    const prompt = data.prompts.find(p => p.id === parseInt(req.params.id));
    const requestedVersion = parseInt(req.params.version);

    if (!prompt) {
      return res.status(404).json({ error: 'Prompt not found' });
    }

    // Check if it's the current version
    if (requestedVersion === prompt.version) {
      return res.json({
        version: prompt.version,
        content: prompt.content,
        updatedAt: prompt.updatedAt
      });
    }

    // Check in history
    if (prompt.history) {
      const historicalVersion = prompt.history.find(h => h.version === requestedVersion);
      if (historicalVersion) {
        return res.json(historicalVersion);
      }
    }

    res.status(404).json({ error: 'Version not found' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch prompt version' });
  }
});

module.exports = router;
